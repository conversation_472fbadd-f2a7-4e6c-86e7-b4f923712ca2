{"version": "0.1.0", "debug_mode": false, "log_level": "INFO", "database": {"type": "sqlite", "db_name": "salary_system.db", "db_path": "data/db", "path": "data/db/salary_system.db", "backup_enabled": true, "backup_count": 5, "connection_timeout": 30}, "import_settings": {"supported_formats": [".xlsx", ".xls", ".csv"], "max_file_size_mb": 100, "chunk_size": 1000, "encoding_priority": ["utf-8", "gbk", "gb2312"]}, "field_mapping": {"auto_generate_mappings": true, "force_mapping_for_unknown_sheets": true, "default_excel_header_as_display_name": true, "preserve_chinese_headers": true, "fuzzy_match_threshold": 0.7, "save_auto_generated_mappings": true}, "processing": {"match_threshold": 0.95, "calculation_precision": 2, "async_processing": true, "max_workers": 4}, "ui": {"theme": "default", "window_width": 1200, "window_height": 800, "auto_save_interval": 300, "show_progress": true}, "reports": {"default_template_dir": "template", "output_dir": "output", "auto_open_report": true, "export_formats": ["docx", "pdf", "xlsx"]}, "cache": {"enabled": true, "ttl_seconds": 600, "max_entries": 200, "max_memory_mb": 200, "preload_enabled": true, "preload_pages": 2}, "user_configurations": {"test_config": {"data": {"file_path": "test.xlsx", "table_type": "salary_data_2025_05", "mapping_config": {"姓名": "name", "工号": "employee_id", "基本工资": "basic_salary"}}, "created_at": "2025-09-07T22:53:21.356587", "version": "0.1.0"}, "import_config_工资表_21fields": {"data": {"file_path": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls", "table_type": "工资表", "selected_sheets": [{"enabled": false, "name": "全部在职人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "s333"}, {"enabled": false, "name": "A岗职工", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "a444"}], "import_strategy": "separate_tables", "mapping_config": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "text_string", "data_type": "string", "is_required": false}, "2025年岗位工资": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "2025年校龄工资": {"target_field": "2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "2025年基础性绩效": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "2025年生活补贴": {"target_field": "2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "text_string", "data_type": "string", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "2025公积金": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "salary_float", "data_type": "float", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false}}, "session_info": {"created_time": "2025-09-13 15:23:28,665", "file_name": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls"}}, "created_at": "2025-09-13T15:23:53.999504", "version": "0.1.0"}, "import_config_工资表_16fields": {"data": {"file_path": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls", "table_type": "工资表", "selected_sheets": [{"enabled": false, "name": "离休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "retired"}, {"enabled": false, "name": "退休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "pension"}, {"enabled": false, "name": "全部在职人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "all_active"}, {"enabled": false, "name": "A岗职工", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "a_grade"}], "import_strategy": "separate_tables", "mapping_config": {"序号": {"target_field": "姓名", "display_name": "序号", "field_type": "name_string", "data_type": "string", "is_required": false}, "人员代码": {"target_field": "organization", "display_name": "人员代码", "field_type": "text_string", "data_type": "string", "is_required": false}, "姓名": {"target_field": "money", "display_name": "姓名", "field_type": "salary_float", "data_type": "string", "is_required": false}, "部门名称": {"target_field": "money", "display_name": "部门名称", "field_type": "salary_float", "data_type": "string", "is_required": false}, "基本\n离休费": {"target_field": "money", "display_name": "基本离休费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "结余\n津贴": {"target_field": "money", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "生活\n补贴": {"target_field": "money", "display_name": "生活补贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "住房\n补贴": {"target_field": "money", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "物业\n补贴": {"target_field": "money", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "离休\n补贴": {"target_field": "序号", "display_name": "离休补贴", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "护理费": {"target_field": "人员代码", "display_name": "护理费", "field_type": "employee_id_string", "data_type": "float", "is_required": false}, "增发一次\n性生活补贴": {"target_field": "基本离休费", "display_name": "增发一次性生活补贴", "field_type": "text_string", "data_type": "string", "is_required": false}, "补发": {"target_field": "护理费", "display_name": "补发", "field_type": "text_string", "data_type": "float", "is_required": false}, "合计": {"target_field": "补发", "display_name": "合计", "field_type": "salary_float", "data_type": "float", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false}, "备注": {"target_field": "备注", "display_name": "备注", "field_type": "text_string", "data_type": "string", "is_required": false}}, "session_info": {"created_time": null, "file_name": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls"}}, "created_at": "2025-09-16T08:23:57.916130", "version": "0.1.0"}, "import_config_工资表_27fields": {"data": {"file_path": "E:\\project\\case\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls", "table_type": "工资表", "selected_sheets": [{"enabled": false, "name": "离休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "r444"}, {"enabled": false, "name": "退休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "p222"}], "import_strategy": "separate_tables", "mapping_config": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "人员代码": {"target_field": "人员代码", "display_name": "人员代码", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "text_string", "data_type": "string", "is_required": false}, "基本退休费": {"target_field": "基本退休费", "display_name": "基本退休费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "离退休生活补贴": {"target_field": "离退休生活补贴", "display_name": "离退休生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "护理费": {"target_field": "护理费", "display_name": "护理费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "物业补贴": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "住房补贴": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "增资预付": {"target_field": "增资预付", "display_name": "增资预付", "field_type": "text_string", "data_type": "string", "is_required": false}, "2016待遇调整": {"target_field": "2016待遇调整", "display_name": "2016待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false}, "2017待遇调整": {"target_field": "2017待遇调整", "display_name": "2017待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false}, "2018待遇调整": {"target_field": "2018待遇调整", "display_name": "2018待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false}, "2019待遇调整": {"target_field": "2019待遇调整", "display_name": "2019待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false}, "2020待遇调整": {"target_field": "2020待遇调整", "display_name": "2020待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false}, "2021待遇调整": {"target_field": "2021待遇调整", "display_name": "2021待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false}, "2022待遇调整": {"target_field": "2022待遇调整", "display_name": "2022待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false}, "2023待遇调整": {"target_field": "2023待遇调整", "display_name": "2023待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "公积": {"target_field": "公积", "display_name": "公积", "field_type": "text_string", "data_type": "string", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "salary_float", "data_type": "float", "is_required": false}, "备注": {"target_field": "备注", "display_name": "备注", "field_type": "text_string", "data_type": "string", "is_required": false}}, "session_info": {"created_time": "2025-09-14 16:27:37,756", "file_name": "E:\\project\\case\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls"}}, "created_at": "2025-09-14T16:30:38.626584", "version": "0.1.0"}, "import_config_异动表_21fields": {"data": {"file_path": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls", "table_type": "异动表", "selected_sheets": [{"detected_type": "change_data", "enabled": false, "name": "离休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "retired"}, {"detected_type": "change_data", "enabled": false, "name": "退休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "pension"}, {"detected_type": "change_data", "enabled": false, "name": "全部在职人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "all_active"}, {"detected_type": "change_data", "enabled": false, "name": "A岗职工", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "a_grade"}], "import_strategy": "separate_tables", "mapping_config": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "text_string", "data_type": "string", "is_required": false}, "2025年岗位工资": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "string", "is_required": false}, "2025年校龄工资": {"target_field": "2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "salary_float", "data_type": "string", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "2025年基础性绩效": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "2025年生活补贴": {"target_field": "2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "salary_float", "data_type": "string", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "text_string", "data_type": "string", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "string", "is_required": false}, "2025公积金": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "salary_float", "data_type": "float", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false}}, "session_info": {"created_time": "2025-09-16 08:24:02,909", "file_name": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls"}}, "created_at": "2025-09-16T08:24:14.507552", "version": "0.1.0"}, "import_config_异动表_16fields": {"data": {"file_path": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls", "table_type": "异动表", "selected_sheets": [{"detected_type": "change_data", "enabled": false, "name": "离休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "r444"}, {"detected_type": "change_data", "enabled": false, "name": "退休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "p222"}, {"detected_type": "change_data", "enabled": false, "name": "全部在职人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "s333"}, {"detected_type": "change_data", "enabled": false, "name": "A岗职工", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "a444"}], "import_strategy": "separate_tables", "mapping_config": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "人员代码": {"target_field": "人员代码", "display_name": "人员代码", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false}, "基本\n离休费": {"target_field": "基本离休费", "display_name": "基本离休费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "结余\n津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "生活\n补贴": {"target_field": "生活补贴", "display_name": "生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "住房\n补贴": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "物业\n补贴": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "离休\n补贴": {"target_field": "离休补贴", "display_name": "离休补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "护理费": {"target_field": "护理费", "display_name": "护理费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "增发一次\n性生活补贴": {"target_field": "增发一次性生活补贴", "display_name": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "合计": {"target_field": "合计", "display_name": "合计", "field_type": "salary_float", "data_type": "float", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false}, "备注": {"target_field": "备注", "display_name": "备注", "field_type": "text_string", "data_type": "string", "is_required": false}}, "session_info": {"created_time": "2025-09-13 15:22:31,660", "file_name": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls"}}, "created_at": "2025-09-13T15:22:45.500701", "version": "0.1.0"}, "import_config_工资表_23fields": {"data": {"file_path": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls", "table_type": "工资表", "selected_sheets": [{"enabled": false, "name": "离休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "r111"}, {"enabled": false, "name": "退休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "p111"}, {"enabled": false, "name": "全部在职人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "s111"}, {"enabled": false, "name": "A岗职工", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "english_name": "a111"}], "import_strategy": "separate_tables", "mapping_config": {"序号": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757592805.818938, "display_name": "序号"}, "工号": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757592805.818938, "display_name": "工号"}, "姓名": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757592805.818938, "display_name": "姓名"}, "部门名称": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757592805.818938, "display_name": "部门名称"}, "人员类别代码": {"target_field": "人员类别代码", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757592805.818938, "display_name": "人员类别代码"}, "人员类别": {"target_field": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757592805.818938, "display_name": "人员类别"}, "2025年岗位工资": {"target_field": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.818938, "display_name": "2025年岗位工资"}, "2025年薪级工资": {"target_field": "2025年薪级工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.818938, "display_name": "2025年薪级工资"}, "津贴": {"target_field": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.818938, "display_name": "津贴"}, "结余津贴": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.818938, "display_name": "结余津贴"}, "2025年基础性绩效": {"target_field": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.818938, "display_name": "2025年基础性绩效"}, "卫生费": {"target_field": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.818938, "display_name": "卫生费"}, "交通补贴": {"target_field": "交通补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.818938, "display_name": "交通补贴"}, "物业补贴": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.8199816, "display_name": "物业补贴"}, "住房补贴": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.8199816, "display_name": "住房补贴"}, "车补": {"target_field": "车补", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757592805.8199816, "display_name": "车补"}, "通讯补贴": {"target_field": "通讯补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.8199816, "display_name": "通讯补贴"}, "2025年奖励性绩效预发": {"target_field": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.8199816, "display_name": "2025年奖励性绩效预发"}, "补发": {"target_field": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.8199816, "display_name": "补发"}, "借支": {"target_field": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.8199816, "display_name": "借支"}, "应发工资": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.8199816, "display_name": "应发工资"}, "2025公积金": {"target_field": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.8199816, "display_name": "2025公积金"}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757592805.8199816, "display_name": "代扣代存养老保险"}}, "session_info": {"created_time": "2025-09-11 20:12:34,796", "file_name": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls"}}, "created_at": "2025-09-11T20:14:41.825606", "version": "0.1.0"}, "import_config_异动表_23fields": {"data": {"file_path": "E:\\project\\case\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls", "table_type": "异动表", "selected_sheets": [{"detected_type": "change_data", "enabled": false, "name": "全部在职人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "s333"}, {"detected_type": "change_data", "enabled": false, "name": "A岗职工", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "a444"}], "import_strategy": "separate_tables", "mapping_config": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "text_string", "data_type": "string", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false}, "2025年岗位工资": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "2025年薪级工资": {"target_field": "2025年薪级工资", "display_name": "2025年薪级工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "2025年基础性绩效": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "交通补贴": {"target_field": "交通补贴", "display_name": "交通补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "物业补贴": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "住房补贴": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "text_string", "data_type": "string", "is_required": false}, "通讯补贴": {"target_field": "通讯补贴", "display_name": "通讯补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "2025公积金": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false}}, "session_info": {"created_time": "2025-09-14 16:30:42,472", "file_name": "E:\\project\\case\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls"}}, "created_at": "2025-09-14T16:31:09.599056", "version": "0.1.0"}, "import_config_异动表_27fields": {"data": {"file_path": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls", "table_type": "异动表", "selected_sheets": [{"detected_type": "change_data", "enabled": false, "name": "离休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "r444"}, {"detected_type": "change_data", "enabled": false, "name": "退休人员工资表", "row_count": 0, "status": "error: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "tooltip": "异动表：支持灵活字段配置", "english_name": "p222"}], "import_strategy": "separate_tables", "mapping_config": {"序号": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "序号"}, "人员代码": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "人员代码"}, "姓名": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "姓名"}, "部门名称": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "部门名称"}, "人员类别代码": {"target_field": "人员类别代码", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "人员类别代码"}, "基本退休费": {"target_field": "基本退休费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "基本退休费"}, "津贴": {"target_field": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "津贴"}, "结余津贴": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "结余津贴"}, "离退休生活补贴": {"target_field": "离退休生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "离退休生活补贴"}, "护理费": {"target_field": "护理费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "护理费"}, "物业补贴": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "物业补贴"}, "住房补贴": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "住房补贴"}, "增资预付": {"target_field": "增资预付", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "增资预付"}, "2016待遇调整": {"target_field": "2016待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "2016待遇调整"}, "2017待遇调整": {"target_field": "2017待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "2017待遇调整"}, "2018待遇调整": {"target_field": "2018待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "2018待遇调整"}, "2019待遇调整": {"target_field": "2019待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "2019待遇调整"}, "2020待遇调整": {"target_field": "2020待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "2020待遇调整"}, "2021待遇调整": {"target_field": "2021待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "2021待遇调整"}, "2022待遇调整": {"target_field": "2022待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "2022待遇调整"}, "2023待遇调整": {"target_field": "2023待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "2023待遇调整"}, "补发": {"target_field": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "补发"}, "借支": {"target_field": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "借支"}, "应发工资": {"target_field": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "应发工资"}, "公积": {"target_field": "公积", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "公积"}, "保险扣款": {"target_field": "保险扣款", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "保险扣款"}, "备注": {"target_field": "备注", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757748051.4276192, "display_name": "备注"}}, "session_info": {"created_time": "2025-09-13 15:20:51,427", "file_name": "C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls"}}, "created_at": "2025-09-13T15:21:19.948620", "version": "0.1.0"}}, "table_mappings": {"mapping_config_离休人员工资表": {"metadata": {"created_at": "2025-09-13T22:28:23.602759", "last_modified": "2025-09-14T18:36:23.163046", "auto_generated": false, "user_modified": true}, "field_mappings": {"借支": "借支", "增发一次\n性生活补贴": "增发一次性生活补贴", "生活\n补贴": "生活补贴", "基本\n离休费": "基本离休费", "住房\n补贴": "住房补贴", "护理费": "护理费", "离休\n补贴": "离休补贴", "补发": "补发", "物业\n补贴": "物业补贴", "结余\n津贴": "结余津贴", "合计": "合计", "序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "备注": "备注"}, "field_configs": {"借支": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437}, "增发一次\n性生活补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437}, "生活\n补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437}, "基本\n离休费": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437}, "住房\n补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437}, "护理费": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437}, "离休\n补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437}, "补发": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437}, "物业\n补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437}, "结余\n津贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437}, "合计": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437}, "序号": {"field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757846183.1530437}, "人员代码": {"field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757846183.1530437}, "姓名": {"field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757846183.1530437}, "部门名称": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846183.1530437}, "备注": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846183.1530437}}, "edit_history": [{"timestamp": "2025-09-13T22:46:38.140994", "field": "住房\n补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757774798.1200738}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T22:46:38.140994", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "display_name": "护理费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757774798.1200738}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T22:46:38.140994", "field": "结余\n津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757774798.1200738}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T22:46:38.140994", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757774798.1200738}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T22:46:38.140510", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757774798.1200738, "display_name": "序号"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-13T22:46:38.140510", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757774798.1200738, "display_name": "人员代码"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-13T22:46:38.140510", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757774798.1200738, "display_name": "姓名"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-13T22:46:38.140510", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757774798.1200738, "display_name": "部门名称"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-13T22:46:38.140994", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757774798.1200738, "display_name": "备注"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "基本\n离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "display_name": "基本离休费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777525.2021904}, "user_action": false, "compressed": true, "merged_count": 5}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "增发一次\n性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "display_name": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777525.2021904}, "user_action": false, "compressed": true, "merged_count": 5}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "离休\n补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "display_name": "离休补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777525.2021904}, "user_action": false, "compressed": true, "merged_count": 5}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "住房\n补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777525.2021904}, "user_action": false, "compressed": true, "merged_count": 5}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777525.2021904}, "user_action": false, "compressed": true, "merged_count": 5}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "结余\n津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777525.2021904}, "user_action": false, "compressed": true, "merged_count": 5}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "物业\n补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777525.2021904}, "user_action": false, "compressed": true, "merged_count": 5}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "display_name": "合计", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777525.2021904}, "user_action": false, "compressed": true, "merged_count": 5}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "display_name": "护理费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777525.2021904}, "user_action": false, "compressed": true, "merged_count": 5}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "生活\n补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "display_name": "生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777525.2021904}, "user_action": false, "compressed": true, "merged_count": 5}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777525.2021904}, "user_action": false, "compressed": true, "merged_count": 5}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757777525.2021904, "display_name": "序号"}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757777525.2021904, "display_name": "人员代码"}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757777525.2021904, "display_name": "姓名"}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757777525.2021904, "display_name": "部门名称"}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.217840", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757777525.2021904, "display_name": "备注"}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "display_name": "护理费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812396.2558382}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "生活\n补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "display_name": "生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812396.2558382}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "离休\n补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "display_name": "离休补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812396.2558382}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "物业\n补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812396.2558382}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "住房\n补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812396.2558382}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "结余\n津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812396.2558382}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812396.2558382}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "基本\n离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "display_name": "基本离休费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812396.2558382}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "增发一次\n性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "display_name": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812396.2558382}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812396.2558382}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "display_name": "合计", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812396.2558382}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757812396.2558382, "display_name": "序号"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757812396.2558382, "display_name": "人员代码"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757812396.2558382, "display_name": "姓名"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757812396.2558382, "display_name": "部门名称"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:16.276409", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757812396.2558382, "display_name": "备注"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "增发一次\n性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "display_name": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757817855.716458}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757817855.716458}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "物业\n补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757817855.716458}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "结余\n津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757817855.716458}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "display_name": "合计", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757817855.716458}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "离休\n补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "display_name": "离休补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757817855.716458}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "基本\n离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "display_name": "基本离休费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757817855.716458}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "生活\n补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "display_name": "生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757817855.716458}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "display_name": "护理费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757817855.716458}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757817855.716458}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "住房\n补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757817855.716458}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757817855.716458, "display_name": "序号"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757817855.716458, "display_name": "人员代码"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757817855.716458, "display_name": "姓名"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757817855.716458, "display_name": "部门名称"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T10:44:15.726436", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757817855.716458, "display_name": "备注"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "基本\n离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "display_name": "基本离休费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838629.5140681}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838629.5140681}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "住房\n补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838629.5140681}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "生活\n补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "display_name": "生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838629.5140681}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "display_name": "护理费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838629.5140681}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "增发一次\n性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "display_name": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838629.5140681}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838629.5140681}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "离休\n补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "display_name": "离休补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838629.5140681}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "display_name": "合计", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838629.5140681}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "物业\n补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838629.5140681}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "结余\n津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838629.5140681}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757838629.5140681, "display_name": "序号"}, "user_action": false}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757838629.5140681, "display_name": "人员代码"}, "user_action": false}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757838629.5140681, "display_name": "姓名"}, "user_action": false}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838629.5140681, "display_name": "部门名称"}, "user_action": false}, {"timestamp": "2025-09-14T16:30:29.524073", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838629.5140681, "display_name": "备注"}, "user_action": false}, {"timestamp": "2025-09-14T18:24:58.295738", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T18:24:58.295738", "field": "住房\n补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T18:24:58.295738", "field": "基本\n离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "display_name": "基本离休费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T18:24:58.295738", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T18:24:58.295738", "field": "物业\n补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T18:24:58.295738", "field": "离休\n补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "display_name": "离休补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T18:24:58.295738", "field": "增发一次\n性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "display_name": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T18:24:58.295738", "field": "结余\n津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T18:24:58.295738", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "display_name": "护理费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T18:24:58.295738", "field": "生活\n补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "display_name": "生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T18:24:58.295738", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "display_name": "合计", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "序号"}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "人员代码"}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "姓名"}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "部门名称"}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "基本\n离休费", "action": "field_config_update", "config": {"target_field": "基本离休费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "基本离休费"}, "user_action": false, "compressed": true, "merged_count": 4}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "结余\n津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "结余津贴"}, "user_action": false, "compressed": true, "merged_count": 4}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "生活\n补贴", "action": "field_config_update", "config": {"target_field": "生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "生活补贴"}, "user_action": false, "compressed": true, "merged_count": 4}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "住房\n补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "住房补贴"}, "user_action": false, "compressed": true, "merged_count": 4}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "物业\n补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "物业补贴"}, "user_action": false, "compressed": true, "merged_count": 4}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "离休\n补贴", "action": "field_config_update", "config": {"target_field": "离休补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "离休补贴"}, "user_action": false, "compressed": true, "merged_count": 4}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "护理费"}, "user_action": false, "compressed": true, "merged_count": 4}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "增发一次\n性生活补贴", "action": "field_config_update", "config": {"target_field": "增发一次性生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "增发一次性生活补贴"}, "user_action": false, "compressed": true, "merged_count": 4}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "补发"}, "user_action": false, "compressed": true, "merged_count": 4}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "合计", "action": "field_config_update", "config": {"target_field": "合计", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "合计"}, "user_action": false, "compressed": true, "merged_count": 4}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "借支"}, "user_action": false, "compressed": true, "merged_count": 4}, {"timestamp": "2025-09-14T18:36:23.163046", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846183.1530437, "display_name": "备注"}, "user_action": false, "compressed": true, "merged_count": 3}]}, "salary_data_2025_05_retired_employees": {"field_mappings": {"sequence_number": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "序号", "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "employee_id": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "人员代码", "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "employee_name": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "姓名", "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "department": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "部门名称", "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "basic_retirement_salary": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "基本离休费", "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "balance_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "结余津贴", "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "living_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "生活补贴", "display_name": "living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance", "field_type": "", "data_type": "", "is_required": false}, "housing_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "住房补贴", "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "property_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "物业补贴", "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "retirement_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "离休补贴", "display_name": "retirement_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_allowance", "field_type": "", "data_type": "", "is_required": false}, "nursing_fee": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "护理费", "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "one_time_living_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "增发一次性生活补贴", "display_name": "one_time_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "one_time_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "one_time_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "one_time_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "one_time_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "one_time_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "supplement": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "补发", "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "total": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "合计", "display_name": "total", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total", "field_type": "", "data_type": "", "is_required": false}, "advance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "借支", "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "remarks": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "备注", "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "id": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "自增主键", "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "month": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "月份", "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "year": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "年份", "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "created_at": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "创建时间", "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "updated_at": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "更新时间", "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}}, "original_excel_headers": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "basic_retirement_salary": "基本\n离休费", "balance_allowance": "结余\n津贴", "living_allowance": "生活\n补贴", "housing_allowance": "住房\n补贴", "property_allowance": "物业\n补贴", "retirement_allowance": "离休\n补贴", "nursing_fee": "护理费", "one_time_living_allowance": "增发一次\n性生活补贴", "supplement": "补发", "total": "合计", "advance": "借支", "remarks": "备注"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-14T18:35:36.930620", "sheet_name": "离休人员工资表", "has_chinese_headers": true}}, "salary_data_2025_05_pension_employees": {"field_mappings": {"sequence_number": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "序号", "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "employee_id": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "人员代码", "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "employee_name": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "姓名", "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "department": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "部门名称", "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "employee_type_code": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "人员类别代码", "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "basic_retirement_salary": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "基本退休费", "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "津贴", "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "balance_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "结余津贴", "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "retirement_living_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "离退休生活补贴", "display_name": "retirement_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "nursing_fee": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "护理费", "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "property_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "物业补贴", "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "housing_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "住房补贴", "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "salary_advance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "增资预付", "display_name": "salary_advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "salary_advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "salary_advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "salary_advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "salary_advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "salary_advance", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2016": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2016待遇调整", "display_name": "adjustment_2016", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2016", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2016", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2016", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2016", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2016", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2017": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2017待遇调整", "display_name": "adjustment_2017", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2017", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2017", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2017", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2017", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2017", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2018": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2018待遇调整", "display_name": "adjustment_2018", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2018", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2018", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2018", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2018", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2018", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2019": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2019待遇调整", "display_name": "adjustment_2019", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2019", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2019", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2019", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2019", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2019", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2020": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2020待遇调整", "display_name": "adjustment_2020", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2020", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2020", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2020", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2020", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2020", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2021": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2021待遇调整", "display_name": "adjustment_2021", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2021", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2021", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2021", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2021", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2021", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2022": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2022待遇调整", "display_name": "adjustment_2022", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2022", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2022", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2022", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2022", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2022", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2023": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2023待遇调整", "display_name": "adjustment_2023", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2023", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2023", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2023", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2023", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2023", "field_type": "", "data_type": "", "is_required": false}, "supplement": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "补发", "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "advance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "借支", "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "total_salary": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "应发工资", "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "provident_fund": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "公积", "display_name": "provident_fund", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund", "field_type": "", "data_type": "", "is_required": false}, "insurance_deduction": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "保险扣款", "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "remarks": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "备注", "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "id": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "自增主键", "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "month": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "月份", "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "year": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "年份", "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "created_at": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "创建时间", "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "updated_at": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "更新时间", "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}}, "original_excel_headers": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "basic_retirement_salary": "基本退休费", "allowance": "津贴", "balance_allowance": "结余津贴", "retirement_living_allowance": "离退休生活补贴", "nursing_fee": "护理费", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "salary_advance": "增资预付", "adjustment_2016": "2016待遇调整", "adjustment_2017": "2017待遇调整", "adjustment_2018": "2018待遇调整", "adjustment_2019": "2019待遇调整", "adjustment_2020": "2020待遇调整", "adjustment_2021": "2021待遇调整", "adjustment_2022": "2022待遇调整", "adjustment_2023": "2023待遇调整", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund": "公积", "insurance_deduction": "保险扣款", "remarks": "备注"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-14T18:35:37.040649", "sheet_name": "退休人员工资表", "has_chinese_headers": true}}, "salary_data_2025_05_active_employees": {"field_mappings": {"sequence_number": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "序号", "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "employee_id": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "工号", "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "employee_name": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "姓名", "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "department": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "部门名称", "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "employee_type_code": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "人员类别代码", "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "employee_type": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "人员类别", "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "position_salary_2025": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2025年岗位工资", "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "grade_salary_2025": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2025年薪级工资", "display_name": "grade_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "grade_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "grade_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "grade_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "grade_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "grade_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "津贴", "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "balance_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "结余津贴", "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "basic_performance_2025": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2025年基础性绩效", "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "health_fee": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "卫生费", "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "transport_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "交通补贴", "display_name": "transport_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "transport_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "transport_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "transport_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "transport_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "transport_allowance", "field_type": "", "data_type": "", "is_required": false}, "property_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "物业补贴", "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "housing_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "住房补贴", "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "car_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "车补", "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "communication_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "通讯补贴", "display_name": "communication_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "communication_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "communication_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "communication_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "communication_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "communication_allowance", "field_type": "", "data_type": "", "is_required": false}, "performance_bonus_2025": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2025年奖励性绩效预发", "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "supplement": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "补发", "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "advance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "借支", "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "total_salary": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "应发工资", "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "provident_fund_2025": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2025公积金", "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "pension_insurance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "代扣代存养老保险", "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "id": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "自增主键", "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "month": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "月份", "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "year": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "年份", "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "created_at": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "创建时间", "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "updated_at": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "更新时间", "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}}, "original_excel_headers": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "employee_type": "人员类别", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-14T18:35:37.150800", "sheet_name": "全部在职人员工资表", "has_chinese_headers": true}}, "salary_data_2025_05_a_grade_employees": {"field_mappings": {"sequence_number": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "序号", "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "employee_id": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "工号", "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "employee_name": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "姓名", "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "department": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "部门名称", "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "employee_type": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "人员类别", "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type", "field_type": "", "data_type": "", "is_required": false}, "employee_type_code": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "人员类别代码", "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "position_salary_2025": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2025年岗位工资", "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "position_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "seniority_salary_2025": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2025年校龄工资", "display_name": "seniority_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "seniority_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "seniority_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "seniority_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "seniority_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "seniority_salary_2025", "field_type": "", "data_type": "", "is_required": false}, "allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "津贴", "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "balance_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "结余津贴", "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "basic_performance_2025": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2025年基础性绩效", "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_performance_2025", "field_type": "", "data_type": "", "is_required": false}, "health_fee": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "卫生费", "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "health_fee", "field_type": "", "data_type": "", "is_required": false}, "living_allowance_2025": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2025年生活补贴", "display_name": "living_allowance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance_2025", "field_type": "", "data_type": "", "is_required": false}, "car_allowance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "车补", "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "car_allowance", "field_type": "", "data_type": "", "is_required": false}, "performance_bonus_2025": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2025年奖励性绩效预发", "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "performance_bonus_2025", "field_type": "", "data_type": "", "is_required": false}, "supplement": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "补发", "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "advance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "借支", "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "total_salary": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "应发工资", "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "provident_fund_2025": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "2025公积金", "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund_2025", "field_type": "", "data_type": "", "is_required": false}, "insurance_deduction": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "保险扣款", "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "pension_insurance": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "代扣代存养老保险", "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "pension_insurance", "field_type": "", "data_type": "", "is_required": false}, "id": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "自增主键", "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "month": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "月份", "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "year": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "年份", "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "created_at": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "创建时间", "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "updated_at": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": {"target_field": "更新时间", "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}}, "original_excel_headers": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "seniority_salary_2025": "2025年校龄工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "living_allowance_2025": "2025年生活补贴", "car_allowance": "车补", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "insurance_deduction": "保险扣款", "pension_insurance": "代扣代存养老保险"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-14T18:35:37.300962", "sheet_name": "A岗职工", "has_chinese_headers": true}}, "mapping_config_A岗职工": {"metadata": {"created_at": "2025-09-13T22:46:38.777243", "last_modified": "2025-09-14T18:36:23.835337", "auto_generated": false, "user_modified": true}, "field_mappings": {"2025年岗位工资": "2025年岗位工资", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年校龄工资": "2025年校龄工资", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "借支": "借支", "保险扣款": "保险扣款", "津贴": "津贴", "结余津贴": "结余津贴", "应发工资": "应发工资", "代扣代存养老保险": "代扣代存养老保险", "2025年生活补贴": "2025年生活补贴", "2025公积金": "2025公积金", "补发": "补发", "序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "车补": "车补"}, "field_configs": {"2025年岗位工资": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "2025年基础性绩效": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "卫生费": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "2025年校龄工资": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "2025年奖励性绩效预发": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "借支": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "保险扣款": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "津贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "结余津贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "应发工资": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "代扣代存养老保险": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "2025年生活补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "2025公积金": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "补发": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846183.835337}, "序号": {"field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755}, "工号": {"field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755}, "姓名": {"field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755}, "部门名称": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755}, "人员类别": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755}, "人员类别代码": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755}, "车补": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755}}, "edit_history": [{"timestamp": "2025-09-13T22:46:38.777243", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-13T22:46:38.777243", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:05.856923", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757777520.2320743}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-13T23:32:00.232074", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757777520.2320743, "display_name": "序号"}, "user_action": false}, {"timestamp": "2025-09-13T23:32:00.232074", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757777520.2320743, "display_name": "工号"}, "user_action": false}, {"timestamp": "2025-09-13T23:32:00.232074", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757777520.2320743, "display_name": "姓名"}, "user_action": false}, {"timestamp": "2025-09-13T23:32:00.232074", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757777520.2320743, "display_name": "部门名称"}, "user_action": false}, {"timestamp": "2025-09-13T23:32:00.232074", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757777520.2320743, "display_name": "人员类别"}, "user_action": false}, {"timestamp": "2025-09-13T23:32:00.232074", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757777520.2320743, "display_name": "人员类别代码"}, "user_action": false}, {"timestamp": "2025-09-13T23:32:00.232074", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757777520.2320743, "display_name": "车补"}, "user_action": false}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757812410.5817125}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757812410.5817125, "display_name": "序号"}, "user_action": false}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757812410.5817125, "display_name": "工号"}, "user_action": false}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757812410.5817125, "display_name": "姓名"}, "user_action": false}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757812410.5817125, "display_name": "部门名称"}, "user_action": false}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757812410.5817125, "display_name": "人员类别"}, "user_action": false}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757812410.5817125, "display_name": "人员类别代码"}, "user_action": false}, {"timestamp": "2025-09-14T09:13:30.591716", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757812410.5817125, "display_name": "车补"}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T10:44:16.394196", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838662.5956407}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757838662.5956407, "display_name": "序号"}, "user_action": false}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757838662.5956407, "display_name": "工号"}, "user_action": false}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757838662.5956407, "display_name": "姓名"}, "user_action": false}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838662.5956407, "display_name": "部门名称"}, "user_action": false}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838662.5956407, "display_name": "人员类别"}, "user_action": false}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838662.5956407, "display_name": "人员类别代码"}, "user_action": false}, {"timestamp": "2025-09-14T16:31:02.605640", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838662.5956407, "display_name": "车补"}, "user_action": false}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "2025年校龄工资", "action": "field_config_update", "config": {"target_field": "2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "2025年生活补贴", "action": "field_config_update", "config": {"target_field": "2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:23.835337", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757846177.4952755}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T18:36:17.500275", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755, "display_name": "序号"}, "user_action": false}, {"timestamp": "2025-09-14T18:36:17.500275", "field": "工号", "action": "field_config_update", "config": {"target_field": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755, "display_name": "工号"}, "user_action": false}, {"timestamp": "2025-09-14T18:36:17.500275", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755, "display_name": "姓名"}, "user_action": false}, {"timestamp": "2025-09-14T18:36:17.500275", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755, "display_name": "部门名称"}, "user_action": false}, {"timestamp": "2025-09-14T18:36:17.500275", "field": "人员类别", "action": "field_config_update", "config": {"target_field": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755, "display_name": "人员类别"}, "user_action": false}, {"timestamp": "2025-09-14T18:36:17.500275", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755, "display_name": "人员类别代码"}, "user_action": false}, {"timestamp": "2025-09-14T18:36:17.500275", "field": "车补", "action": "field_config_update", "config": {"target_field": "车补", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757846177.4952755, "display_name": "车补"}, "user_action": false}]}, "change_data_2025_12_retired_employees": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "基本\n离休费": "基本\n离休费", "结余\n津贴": "结余\n津贴", "生活\n补贴": "生活\n补贴", "住房\n补贴": "住房\n补贴", "物业\n补贴": "物业\n补贴", "离休\n补贴": "离休\n补贴", "护理费": "护理费", "增发一次\n性生活补贴": "增发一次\n性生活补贴", "补发": "补发", "合计": "合计", "借支": "借支", "备注": "备注"}, "change_data_2025_12_pension_employees": {"序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "基本退休费": "基本退休费", "津贴": "津贴", "结余津贴": "结余津贴", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "公积": "公积", "保险扣款": "保险扣款", "备注": "备注"}, "change_data_2025_12_active_employees": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "change_data_2025_12_a_grade_employees": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "salary_data_2025_01_retired_employees": {"field_mappings": {"sequence_number": {"target_field": {"target_field": {"target_field": {"target_field": "序号", "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "employee_id": {"target_field": {"target_field": {"target_field": {"target_field": "人员代码", "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "employee_name": {"target_field": {"target_field": {"target_field": {"target_field": "姓名", "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "department": {"target_field": {"target_field": {"target_field": {"target_field": "部门名称", "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "basic_retirement_salary": {"target_field": {"target_field": {"target_field": {"target_field": "基本离休费", "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "balance_allowance": {"target_field": {"target_field": {"target_field": {"target_field": "结余津贴", "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "living_allowance": {"target_field": {"target_field": {"target_field": {"target_field": "生活补贴", "display_name": "living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "living_allowance", "field_type": "", "data_type": "", "is_required": false}, "housing_allowance": {"target_field": {"target_field": {"target_field": {"target_field": "住房补贴", "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "property_allowance": {"target_field": {"target_field": {"target_field": {"target_field": "物业补贴", "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "retirement_allowance": {"target_field": {"target_field": {"target_field": {"target_field": "离休补贴", "display_name": "retirement_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_allowance", "field_type": "", "data_type": "", "is_required": false}, "nursing_fee": {"target_field": {"target_field": {"target_field": {"target_field": "护理费", "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "one_time_living_allowance": {"target_field": {"target_field": {"target_field": {"target_field": "增发一次性生活补贴", "display_name": "one_time_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "one_time_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "one_time_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "one_time_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "supplement": {"target_field": {"target_field": {"target_field": {"target_field": "补发", "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "total": {"target_field": {"target_field": {"target_field": {"target_field": "合计", "display_name": "total", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total", "field_type": "", "data_type": "", "is_required": false}, "advance": {"target_field": {"target_field": {"target_field": {"target_field": "借支", "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "remarks": {"target_field": {"target_field": {"target_field": {"target_field": "备注", "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "id": {"target_field": {"target_field": {"target_field": {"target_field": "自增主键", "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "month": {"target_field": {"target_field": {"target_field": {"target_field": "月份", "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "year": {"target_field": {"target_field": {"target_field": {"target_field": "年份", "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "created_at": {"target_field": {"target_field": {"target_field": {"target_field": "创建时间", "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "updated_at": {"target_field": {"target_field": {"target_field": {"target_field": "更新时间", "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}}, "original_excel_headers": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "basic_retirement_salary": "基本\n离休费", "balance_allowance": "结余\n津贴", "living_allowance": "生活\n补贴", "housing_allowance": "住房\n补贴", "property_allowance": "物业\n补贴", "retirement_allowance": "离休\n补贴", "nursing_fee": "护理费", "one_time_living_allowance": "增发一次\n性生活补贴", "supplement": "补发", "total": "合计", "advance": "借支", "remarks": "备注"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-14T18:35:56.618099", "sheet_name": "离休人员工资表", "has_chinese_headers": true}}, "salary_data_2025_01_pension_employees": {"field_mappings": {"sequence_number": {"target_field": {"target_field": {"target_field": {"target_field": "序号", "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "display_name": "sequence_number", "field_type": "", "data_type": "", "is_required": false}, "employee_id": {"target_field": {"target_field": {"target_field": {"target_field": "人员代码", "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_id", "field_type": "", "data_type": "", "is_required": false}, "employee_name": {"target_field": {"target_field": {"target_field": {"target_field": "姓名", "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_name", "field_type": "", "data_type": "", "is_required": false}, "department": {"target_field": {"target_field": {"target_field": {"target_field": "部门名称", "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "display_name": "department", "field_type": "", "data_type": "", "is_required": false}, "employee_type_code": {"target_field": {"target_field": {"target_field": {"target_field": "人员类别代码", "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "display_name": "employee_type_code", "field_type": "", "data_type": "", "is_required": false}, "basic_retirement_salary": {"target_field": {"target_field": {"target_field": {"target_field": "基本退休费", "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "basic_retirement_salary", "field_type": "", "data_type": "", "is_required": false}, "allowance": {"target_field": {"target_field": {"target_field": {"target_field": "津贴", "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "allowance", "field_type": "", "data_type": "", "is_required": false}, "balance_allowance": {"target_field": {"target_field": {"target_field": {"target_field": "结余津贴", "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "balance_allowance", "field_type": "", "data_type": "", "is_required": false}, "retirement_living_allowance": {"target_field": {"target_field": {"target_field": {"target_field": "离退休生活补贴", "display_name": "retirement_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "retirement_living_allowance", "field_type": "", "data_type": "", "is_required": false}, "nursing_fee": {"target_field": {"target_field": {"target_field": {"target_field": "护理费", "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "display_name": "nursing_fee", "field_type": "", "data_type": "", "is_required": false}, "property_allowance": {"target_field": {"target_field": {"target_field": {"target_field": "物业补贴", "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "property_allowance", "field_type": "", "data_type": "", "is_required": false}, "housing_allowance": {"target_field": {"target_field": {"target_field": {"target_field": "住房补贴", "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "housing_allowance", "field_type": "", "data_type": "", "is_required": false}, "salary_advance": {"target_field": {"target_field": {"target_field": {"target_field": "增资预付", "display_name": "salary_advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "salary_advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "salary_advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "salary_advance", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2016": {"target_field": {"target_field": {"target_field": {"target_field": "2016待遇调整", "display_name": "adjustment_2016", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2016", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2016", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2016", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2017": {"target_field": {"target_field": {"target_field": {"target_field": "2017待遇调整", "display_name": "adjustment_2017", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2017", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2017", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2017", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2018": {"target_field": {"target_field": {"target_field": {"target_field": "2018待遇调整", "display_name": "adjustment_2018", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2018", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2018", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2018", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2019": {"target_field": {"target_field": {"target_field": {"target_field": "2019待遇调整", "display_name": "adjustment_2019", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2019", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2019", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2019", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2020": {"target_field": {"target_field": {"target_field": {"target_field": "2020待遇调整", "display_name": "adjustment_2020", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2020", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2020", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2020", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2021": {"target_field": {"target_field": {"target_field": {"target_field": "2021待遇调整", "display_name": "adjustment_2021", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2021", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2021", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2021", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2022": {"target_field": {"target_field": {"target_field": {"target_field": "2022待遇调整", "display_name": "adjustment_2022", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2022", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2022", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2022", "field_type": "", "data_type": "", "is_required": false}, "adjustment_2023": {"target_field": {"target_field": {"target_field": {"target_field": "2023待遇调整", "display_name": "adjustment_2023", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2023", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2023", "field_type": "", "data_type": "", "is_required": false}, "display_name": "adjustment_2023", "field_type": "", "data_type": "", "is_required": false}, "supplement": {"target_field": {"target_field": {"target_field": {"target_field": "补发", "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "display_name": "supplement", "field_type": "", "data_type": "", "is_required": false}, "advance": {"target_field": {"target_field": {"target_field": {"target_field": "借支", "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "display_name": "advance", "field_type": "", "data_type": "", "is_required": false}, "total_salary": {"target_field": {"target_field": {"target_field": {"target_field": "应发工资", "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "display_name": "total_salary", "field_type": "", "data_type": "", "is_required": false}, "provident_fund": {"target_field": {"target_field": {"target_field": {"target_field": "公积", "display_name": "provident_fund", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund", "field_type": "", "data_type": "", "is_required": false}, "display_name": "provident_fund", "field_type": "", "data_type": "", "is_required": false}, "insurance_deduction": {"target_field": {"target_field": {"target_field": {"target_field": "保险扣款", "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "display_name": "insurance_deduction", "field_type": "", "data_type": "", "is_required": false}, "remarks": {"target_field": {"target_field": {"target_field": {"target_field": "备注", "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "display_name": "remarks", "field_type": "", "data_type": "", "is_required": false}, "id": {"target_field": {"target_field": {"target_field": {"target_field": "自增主键", "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "display_name": "id", "field_type": "", "data_type": "", "is_required": false}, "month": {"target_field": {"target_field": {"target_field": {"target_field": "月份", "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "display_name": "month", "field_type": "", "data_type": "", "is_required": false}, "year": {"target_field": {"target_field": {"target_field": {"target_field": "年份", "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "display_name": "year", "field_type": "", "data_type": "", "is_required": false}, "created_at": {"target_field": {"target_field": {"target_field": {"target_field": "创建时间", "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "created_at", "field_type": "", "data_type": "", "is_required": false}, "updated_at": {"target_field": {"target_field": {"target_field": {"target_field": "更新时间", "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}, "display_name": "updated_at", "field_type": "", "data_type": "", "is_required": false}}, "original_excel_headers": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "basic_retirement_salary": "基本退休费", "allowance": "津贴", "balance_allowance": "结余津贴", "retirement_living_allowance": "离退休生活补贴", "nursing_fee": "护理费", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "salary_advance": "增资预付", "adjustment_2016": "2016待遇调整", "adjustment_2017": "2017待遇调整", "adjustment_2018": "2018待遇调整", "adjustment_2019": "2019待遇调整", "adjustment_2020": "2020待遇调整", "adjustment_2021": "2021待遇调整", "adjustment_2022": "2022待遇调整", "adjustment_2023": "2023待遇调整", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund": "公积", "insurance_deduction": "保险扣款", "remarks": "备注"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-09-14T18:35:56.727621", "sheet_name": "退休人员工资表", "has_chinese_headers": true}}, "change_data_2025_11_active_employees": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "人员类别": "人员类别", "2025年岗位工资": "2025年岗位工资", "2025年薪级工资": "2025年薪级工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "交通补贴": "交通补贴", "物业补贴": "物业补贴", "住房补贴": "住房补贴", "车补": "车补", "通讯补贴": "通讯补贴", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "代扣代存养老保险": "代扣代存养老保险"}, "change_data_2025_11_a_grade_employees": {"序号": "序号", "工号": "工号", "姓名": "姓名", "部门名称": "部门名称", "人员类别": "人员类别", "人员类别代码": "人员类别代码", "2025年岗位工资": "2025年岗位工资", "2025年校龄工资": "2025年校龄工资", "津贴": "津贴", "结余津贴": "结余津贴", "2025年基础性绩效": "2025年基础性绩效", "卫生费": "卫生费", "2025年生活补贴": "2025年生活补贴", "车补": "车补", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "补发": "补发", "借支": "借支", "应发工资": "应发工资", "2025公积金": "2025公积金", "保险扣款": "保险扣款", "代扣代存养老保险": "代扣代存养老保险"}, "mapping_config_全部在职人员工资表": {"metadata": {"created_at": "2025-09-14T09:13:31.232754", "last_modified": "2025-09-14T16:31:03.276763", "auto_generated": false, "user_modified": true}, "field_mappings": {"交通补贴": "交通补贴", "住房补贴": "住房补贴", "代扣代存养老保险": "代扣代存养老保险", "2025年基础性绩效": "2025年基础性绩效", "2025年奖励性绩效预发": "2025年奖励性绩效预发", "应发工资": "应发工资", "结余津贴": "结余津贴", "2025年薪级工资": "2025年薪级工资", "借支": "借支", "2025公积金": "2025公积金", "补发": "补发", "2025年岗位工资": "2025年岗位工资", "卫生费": "卫生费", "物业补贴": "物业补贴", "通讯补贴": "通讯补贴", "津贴": "津贴"}, "field_configs": {"交通补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "住房补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "代扣代存养老保险": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "2025年基础性绩效": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "2025年奖励性绩效预发": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "应发工资": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "结余津贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "2025年薪级工资": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "借支": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "2025公积金": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "补发": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "2025年岗位工资": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "卫生费": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "物业补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "通讯补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}, "津贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838663.2767637}}, "edit_history": [{"timestamp": "2025-09-14T09:13:31.232754", "field": "交通补贴", "action": "field_config_update", "config": {"target_field": "交通补贴", "display_name": "交通补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "2025年薪级工资", "action": "field_config_update", "config": {"target_field": "2025年薪级工资", "display_name": "2025年薪级工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "通讯补贴", "action": "field_config_update", "config": {"target_field": "通讯补贴", "display_name": "通讯补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T09:13:31.232754", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "交通补贴", "action": "field_config_update", "config": {"target_field": "交通补贴", "display_name": "交通补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "2025年奖励性绩效预发", "action": "field_config_update", "config": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "2025公积金", "action": "field_config_update", "config": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "2025年岗位工资", "action": "field_config_update", "config": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "通讯补贴", "action": "field_config_update", "config": {"target_field": "通讯补贴", "display_name": "通讯补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "代扣代存养老保险", "action": "field_config_update", "config": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "2025年薪级工资", "action": "field_config_update", "config": {"target_field": "2025年薪级工资", "display_name": "2025年薪级工资", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "卫生费", "action": "field_config_update", "config": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "2025年基础性绩效", "action": "field_config_update", "config": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:31:03.276763", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false}, "user_action": false, "compressed": true, "merged_count": 2}]}, "mapping_config_退休人员工资表": {"metadata": {"created_at": "2025-09-14T16:30:31.841149", "last_modified": "2025-09-14T16:30:46.381365", "auto_generated": false, "user_modified": true}, "field_mappings": {"保险扣款": "保险扣款", "补发": "补发", "离退休生活补贴": "离退休生活补贴", "护理费": "护理费", "借支": "借支", "结余津贴": "结余津贴", "应发工资": "应发工资", "基本退休费": "基本退休费", "津贴": "津贴", "住房补贴": "住房补贴", "物业补贴": "物业补贴", "序号": "序号", "人员代码": "人员代码", "姓名": "姓名", "部门名称": "部门名称", "人员类别代码": "人员类别代码", "增资预付": "增资预付", "2016待遇调整": "2016待遇调整", "2017待遇调整": "2017待遇调整", "2018待遇调整": "2018待遇调整", "2019待遇调整": "2019待遇调整", "2020待遇调整": "2020待遇调整", "2021待遇调整": "2021待遇调整", "2022待遇调整": "2022待遇调整", "2023待遇调整": "2023待遇调整", "公积": "公积", "备注": "备注"}, "field_configs": {"保险扣款": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "补发": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "离退休生活补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "护理费": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "借支": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "结余津贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "应发工资": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "基本退休费": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "津贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "住房补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "物业补贴": {"field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "序号": {"field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "人员代码": {"field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "姓名": {"field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "部门名称": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "人员类别代码": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "增资预付": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "2016待遇调整": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "2017待遇调整": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "2018待遇调整": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "2019待遇调整": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "2020待遇调整": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "2021待遇调整": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "2022待遇调整": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "2023待遇调整": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "公积": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}, "备注": {"field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677}}, "edit_history": [{"timestamp": "2025-09-14T16:30:46.381365", "field": "保险扣款", "action": "field_config_update", "config": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "补发", "action": "field_config_update", "config": {"target_field": "补发", "display_name": "补发", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "离退休生活补贴", "action": "field_config_update", "config": {"target_field": "离退休生活补贴", "display_name": "离退休生活补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "护理费", "action": "field_config_update", "config": {"target_field": "护理费", "display_name": "护理费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "借支", "action": "field_config_update", "config": {"target_field": "借支", "display_name": "借支", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "结余津贴", "action": "field_config_update", "config": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "应发工资", "action": "field_config_update", "config": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "基本退休费", "action": "field_config_update", "config": {"target_field": "基本退休费", "display_name": "基本退休费", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "津贴", "action": "field_config_update", "config": {"target_field": "津贴", "display_name": "津贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "住房补贴", "action": "field_config_update", "config": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "物业补贴", "action": "field_config_update", "config": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "salary_float", "data_type": "float", "is_required": false, "last_modified": 1757838646.3713677}, "user_action": false, "compressed": true, "merged_count": 3}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "序号", "action": "field_config_update", "config": {"target_field": "序号", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "序号"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "人员代码", "action": "field_config_update", "config": {"target_field": "人员代码", "field_type": "employee_id_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "人员代码"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "姓名", "action": "field_config_update", "config": {"target_field": "姓名", "field_type": "name_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "姓名"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "部门名称", "action": "field_config_update", "config": {"target_field": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "部门名称"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "人员类别代码", "action": "field_config_update", "config": {"target_field": "人员类别代码", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "人员类别代码"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "增资预付", "action": "field_config_update", "config": {"target_field": "增资预付", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "增资预付"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "2016待遇调整", "action": "field_config_update", "config": {"target_field": "2016待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "2016待遇调整"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "2017待遇调整", "action": "field_config_update", "config": {"target_field": "2017待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "2017待遇调整"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "2018待遇调整", "action": "field_config_update", "config": {"target_field": "2018待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "2018待遇调整"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "2019待遇调整", "action": "field_config_update", "config": {"target_field": "2019待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "2019待遇调整"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "2020待遇调整", "action": "field_config_update", "config": {"target_field": "2020待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "2020待遇调整"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "2021待遇调整", "action": "field_config_update", "config": {"target_field": "2021待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "2021待遇调整"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "2022待遇调整", "action": "field_config_update", "config": {"target_field": "2022待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "2022待遇调整"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "2023待遇调整", "action": "field_config_update", "config": {"target_field": "2023待遇调整", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "2023待遇调整"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "公积", "action": "field_config_update", "config": {"target_field": "公积", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "公积"}, "user_action": false, "compressed": true, "merged_count": 2}, {"timestamp": "2025-09-14T16:30:46.381365", "field": "备注", "action": "field_config_update", "config": {"target_field": "备注", "field_type": "text_string", "data_type": "string", "is_required": false, "last_modified": 1757838646.3713677, "display_name": "备注"}, "user_action": false, "compressed": true, "merged_count": 2}]}, "change_data_2025_11_retired_employees": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "", "data_type": "", "is_required": false}, "人员代码": {"target_field": "人员代码", "display_name": "人员代码", "field_type": "", "data_type": "", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "", "data_type": "", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "", "data_type": "", "is_required": false}, "基本\n离休费": {"target_field": "基本\n离休费", "display_name": "基本\n离休费", "field_type": "", "data_type": "", "is_required": false}, "结余\n津贴": {"target_field": "结余\n津贴", "display_name": "结余\n津贴", "field_type": "", "data_type": "", "is_required": false}, "生活\n补贴": {"target_field": "生活\n补贴", "display_name": "生活\n补贴", "field_type": "", "data_type": "", "is_required": false}, "住房\n补贴": {"target_field": "住房\n补贴", "display_name": "住房\n补贴", "field_type": "", "data_type": "", "is_required": false}, "物业\n补贴": {"target_field": "物业\n补贴", "display_name": "物业\n补贴", "field_type": "", "data_type": "", "is_required": false}, "离休\n补贴": {"target_field": "离休\n补贴", "display_name": "离休\n补贴", "field_type": "", "data_type": "", "is_required": false}, "护理费": {"target_field": "护理费", "display_name": "护理费", "field_type": "", "data_type": "", "is_required": false}, "增发一次\n性生活补贴": {"target_field": "增发一次\n性生活补贴", "display_name": "增发一次\n性生活补贴", "field_type": "", "data_type": "", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "", "data_type": "", "is_required": false}, "合计": {"target_field": "合计", "display_name": "合计", "field_type": "", "data_type": "", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "", "data_type": "", "is_required": false}, "备注": {"target_field": "备注", "display_name": "备注", "field_type": "", "data_type": "", "is_required": false}}, "change_data_2025_11_pension_employees": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "", "data_type": "", "is_required": false}, "人员代码": {"target_field": "人员代码", "display_name": "人员代码", "field_type": "", "data_type": "", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "", "data_type": "", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "", "data_type": "", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "", "data_type": "", "is_required": false}, "基本退休费": {"target_field": "基本退休费", "display_name": "基本退休费", "field_type": "", "data_type": "", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "", "data_type": "", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "", "data_type": "", "is_required": false}, "离退休生活补贴": {"target_field": "离退休生活补贴", "display_name": "离退休生活补贴", "field_type": "", "data_type": "", "is_required": false}, "护理费": {"target_field": "护理费", "display_name": "护理费", "field_type": "", "data_type": "", "is_required": false}, "物业补贴": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "", "data_type": "", "is_required": false}, "住房补贴": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "", "data_type": "", "is_required": false}, "增资预付": {"target_field": "增资预付", "display_name": "增资预付", "field_type": "", "data_type": "", "is_required": false}, "2016待遇调整": {"target_field": "2016待遇调整", "display_name": "2016待遇调整", "field_type": "", "data_type": "", "is_required": false}, "2017待遇调整": {"target_field": "2017待遇调整", "display_name": "2017待遇调整", "field_type": "", "data_type": "", "is_required": false}, "2018待遇调整": {"target_field": "2018待遇调整", "display_name": "2018待遇调整", "field_type": "", "data_type": "", "is_required": false}, "2019待遇调整": {"target_field": "2019待遇调整", "display_name": "2019待遇调整", "field_type": "", "data_type": "", "is_required": false}, "2020待遇调整": {"target_field": "2020待遇调整", "display_name": "2020待遇调整", "field_type": "", "data_type": "", "is_required": false}, "2021待遇调整": {"target_field": "2021待遇调整", "display_name": "2021待遇调整", "field_type": "", "data_type": "", "is_required": false}, "2022待遇调整": {"target_field": "2022待遇调整", "display_name": "2022待遇调整", "field_type": "", "data_type": "", "is_required": false}, "2023待遇调整": {"target_field": "2023待遇调整", "display_name": "2023待遇调整", "field_type": "", "data_type": "", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "", "data_type": "", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "", "data_type": "", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "", "data_type": "", "is_required": false}, "公积": {"target_field": "公积", "display_name": "公积", "field_type": "", "data_type": "", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "", "data_type": "", "is_required": false}, "备注": {"target_field": "备注", "display_name": "备注", "field_type": "", "data_type": "", "is_required": false}}, "change_data_2025_10_active_employees": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "", "data_type": "", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "", "data_type": "", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "", "data_type": "", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "", "data_type": "", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "", "data_type": "", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "", "data_type": "", "is_required": false}, "2025年岗位工资": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "", "data_type": "", "is_required": false}, "2025年薪级工资": {"target_field": "2025年薪级工资", "display_name": "2025年薪级工资", "field_type": "", "data_type": "", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "", "data_type": "", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "", "data_type": "", "is_required": false}, "2025年基础性绩效": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "", "data_type": "", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "", "data_type": "", "is_required": false}, "交通补贴": {"target_field": "交通补贴", "display_name": "交通补贴", "field_type": "", "data_type": "", "is_required": false}, "物业补贴": {"target_field": "物业补贴", "display_name": "物业补贴", "field_type": "", "data_type": "", "is_required": false}, "住房补贴": {"target_field": "住房补贴", "display_name": "住房补贴", "field_type": "", "data_type": "", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "", "data_type": "", "is_required": false}, "通讯补贴": {"target_field": "通讯补贴", "display_name": "通讯补贴", "field_type": "", "data_type": "", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "", "data_type": "", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "", "data_type": "", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "", "data_type": "", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "", "data_type": "", "is_required": false}, "2025公积金": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "", "data_type": "", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "", "data_type": "", "is_required": false}}, "change_data_2025_10_a_grade_employees": {"序号": {"target_field": "序号", "display_name": "序号", "field_type": "", "data_type": "", "is_required": false}, "工号": {"target_field": "工号", "display_name": "工号", "field_type": "", "data_type": "", "is_required": false}, "姓名": {"target_field": "姓名", "display_name": "姓名", "field_type": "", "data_type": "", "is_required": false}, "部门名称": {"target_field": "部门名称", "display_name": "部门名称", "field_type": "", "data_type": "", "is_required": false}, "人员类别": {"target_field": "人员类别", "display_name": "人员类别", "field_type": "", "data_type": "", "is_required": false}, "人员类别代码": {"target_field": "人员类别代码", "display_name": "人员类别代码", "field_type": "", "data_type": "", "is_required": false}, "2025年岗位工资": {"target_field": "2025年岗位工资", "display_name": "2025年岗位工资", "field_type": "", "data_type": "", "is_required": false}, "2025年校龄工资": {"target_field": "2025年校龄工资", "display_name": "2025年校龄工资", "field_type": "", "data_type": "", "is_required": false}, "津贴": {"target_field": "津贴", "display_name": "津贴", "field_type": "", "data_type": "", "is_required": false}, "结余津贴": {"target_field": "结余津贴", "display_name": "结余津贴", "field_type": "", "data_type": "", "is_required": false}, "2025年基础性绩效": {"target_field": "2025年基础性绩效", "display_name": "2025年基础性绩效", "field_type": "", "data_type": "", "is_required": false}, "卫生费": {"target_field": "卫生费", "display_name": "卫生费", "field_type": "", "data_type": "", "is_required": false}, "2025年生活补贴": {"target_field": "2025年生活补贴", "display_name": "2025年生活补贴", "field_type": "", "data_type": "", "is_required": false}, "车补": {"target_field": "车补", "display_name": "车补", "field_type": "", "data_type": "", "is_required": false}, "2025年奖励性绩效预发": {"target_field": "2025年奖励性绩效预发", "display_name": "2025年奖励性绩效预发", "field_type": "", "data_type": "", "is_required": false}, "补发": {"target_field": "补发", "display_name": "补发", "field_type": "", "data_type": "", "is_required": false}, "借支": {"target_field": "借支", "display_name": "借支", "field_type": "", "data_type": "", "is_required": false}, "应发工资": {"target_field": "应发工资", "display_name": "应发工资", "field_type": "", "data_type": "", "is_required": false}, "2025公积金": {"target_field": "2025公积金", "display_name": "2025公积金", "field_type": "", "data_type": "", "is_required": false}, "保险扣款": {"target_field": "保险扣款", "display_name": "保险扣款", "field_type": "", "data_type": "", "is_required": false}, "代扣代存养老保险": {"target_field": "代扣代存养老保险", "display_name": "代扣代存养老保险", "field_type": "", "data_type": "", "is_required": false}}}, "last_updated": "2025-09-14T18:36:29.551106", "field_templates": {}}