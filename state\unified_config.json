{"version": "3.0", "global_settings": {"auto_save": true, "sync_enabled": true, "validation_strict": false, "preserve_chinese_headers": true, "auto_generate_mappings": true, "enable_smart_suggestions": true, "save_edit_history": true, "max_history_items": 100}, "field_definitions": {"global": {"employee_id": {"display_name": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": true, "validation_pattern": "^\\d{4,10}$"}, "employee_name": {"display_name": "姓名", "field_type": "name_string", "data_type": "string", "is_required": true}, "department": {"display_name": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false}, "employee_type": {"display_name": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false}, "position_salary_2025": {"display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "grade_salary_2025": {"display_name": "2025年薪级工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "total_salary": {"display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": true, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "year": {"display_name": "年份", "field_type": "year_string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "month_string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "date", "data_type": "datetime", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "date", "data_type": "datetime", "is_required": false}}}, "table_configs": {"salary_data_*": {"inherit_from": "global", "table_type": "salary"}, "change_data_*": {"inherit_from": "global", "table_type": "change"}}, "templates": {"standard_salary": {"name": "标准工资表模板", "description": "适用于标准格式的工资表", "field_definitions": {"employee_id": {"display_name": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": true, "validation_pattern": "^\\d{4,10}$"}, "employee_name": {"display_name": "姓名", "field_type": "name_string", "data_type": "string", "is_required": true}, "department": {"display_name": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false}, "employee_type": {"display_name": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false}, "position_salary_2025": {"display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "grade_salary_2025": {"display_name": "2025年薪级工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "total_salary": {"display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": true, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "year": {"display_name": "年份", "field_type": "year_string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "month_string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "date", "data_type": "datetime", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "date", "data_type": "datetime", "is_required": false}}, "display_order": ["employee_id", "employee_name", "department", "position_salary_2025", "grade_salary_2025", "total_salary"]}, "standard_change": {"name": "标准异动表模板", "description": "适用于标准格式的异动表", "field_definitions": {"employee_id": {"display_name": "工号", "field_type": "employee_id_string", "data_type": "string", "is_required": true, "validation_pattern": "^\\d{4,10}$"}, "employee_name": {"display_name": "姓名", "field_type": "name_string", "data_type": "string", "is_required": true}, "department": {"display_name": "部门名称", "field_type": "text_string", "data_type": "string", "is_required": false}, "employee_type": {"display_name": "人员类别", "field_type": "text_string", "data_type": "string", "is_required": false}, "position_salary_2025": {"display_name": "2025年岗位工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "grade_salary_2025": {"display_name": "2025年薪级工资", "field_type": "salary_float", "data_type": "float", "is_required": false, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "total_salary": {"display_name": "应发工资", "field_type": "salary_float", "data_type": "float", "is_required": true, "format_rules": {"decimal_places": 2, "thousands_separator": true, "trim": true}}, "year": {"display_name": "年份", "field_type": "year_string", "data_type": "string", "is_required": false}, "month": {"display_name": "月份", "field_type": "month_string", "data_type": "string", "is_required": false}, "created_at": {"display_name": "创建时间", "field_type": "date", "data_type": "datetime", "is_required": false}, "updated_at": {"display_name": "更新时间", "field_type": "date", "data_type": "datetime", "is_required": false}, "change_type": {"display_name": "异动类型", "field_type": "text_string", "data_type": "string", "is_required": false}, "change_date": {"display_name": "异动日期", "field_type": "date", "data_type": "date", "is_required": false}}, "display_order": ["employee_id", "employee_name", "department", "change_type", "change_date", "total_salary"]}}, "last_updated": "2025-09-16T11:22:29.302594"}